-- Test script to demonstrate the "off" prefix functionality
-- This script shows how the banking system now handles off-duty jobs

-- Example usage:
-- If you have a business called "catcafe" and an off-duty version "offcatcafe"
-- The "offcatcafe" will use the same "catcafe" bank account
-- The "offcatcafe" account will NOT appear in the banking UI

print("=== FD Banking Off-Duty Account Test ===")

-- Test the getBusinessAccount function
local function testGetBusinessAccount()
    print("\n--- Testing getBusinessAccount function ---")

    -- Test normal business name
    print("Testing normal business: 'catcafe'")
    local normalAccount = exports['fd_banking']:getBusinessAccount('catcafe')
    if normalAccount then
        print("✓ Found account for 'catcafe': " .. normalAccount.name .. " (Balance: $" .. normalAccount.balance .. ")")
    else
        print("✗ No account found for 'catcafe'")
    end

    -- Test off-duty business name
    print("Testing off-duty business: 'offcatcafe'")
    local offAccount = exports['fd_banking']:getBusinessAccount('offcatcafe')
    if offAccount then
        print("✓ Found account for 'offcatcafe': " .. offAccount.name .. " (Balance: $" .. offAccount.balance .. ")")
        print("  → This should be the same as 'catcafe' account")
        print("  → Note: 'offcatcafe' will NOT appear in the banking UI")
    else
        print("✗ No account found for 'offcatcafe'")
    end

    -- Verify they are the same account
    if normalAccount and offAccount then
        if normalAccount.id == offAccount.id then
            print("✓ SUCCESS: Both 'catcafe' and 'offcatcafe' use the same account!")
        else
            print("✗ FAILED: Different accounts returned")
        end
    end
end

-- Test the society account functions
local function testSocietyFunctions()
    print("\n--- Testing Society Account Functions ---")
    
    -- Test GetAccount export
    print("Testing GetAccount export with 'catcafe'")
    local normalBalance = exports['fd_banking']:GetAccount('catcafe')
    print("Balance for 'catcafe': $" .. normalBalance)
    
    print("Testing GetAccount export with 'offcatcafe'")
    local offBalance = exports['fd_banking']:GetAccount('offcatcafe')
    print("Balance for 'offcatcafe': $" .. offBalance)
    
    if normalBalance == offBalance then
        print("✓ SUCCESS: Both return the same balance!")
    else
        print("✗ FAILED: Different balances returned")
    end
end

-- Test string manipulation logic
local function testStringLogic()
    print("\n--- Testing String Logic ---")
    
    local testCases = {
        { input = "catcafe", expected = "catcafe", shouldChange = false },
        { input = "offcatcafe", expected = "catcafe", shouldChange = true },
        { input = "police", expected = "police", shouldChange = false },
        { input = "offpolice", expected = "police", shouldChange = true },
        { input = "mechanic", expected = "mechanic", shouldChange = false },
        { input = "offmechanic", expected = "mechanic", shouldChange = true },
        { input = "of", expected = "of", shouldChange = false }, -- Edge case: too short
        { input = "off", expected = "off", shouldChange = false }, -- Edge case: exactly "off"
    }
    
    for _, testCase in ipairs(testCases) do
        local result = testCase.input
        local changed = false
        
        if string.sub(testCase.input, 1, 3) == "off" and string.len(testCase.input) > 3 then
            result = string.sub(testCase.input, 4)
            changed = true
        end
        
        local status = (result == testCase.expected and changed == testCase.shouldChange) and "✓" or "✗"
        print(string.format("%s '%s' → '%s' (changed: %s)", status, testCase.input, result, tostring(changed)))
    end
end

-- Run tests
testStringLogic()

-- Only run business account tests if we have access to the database
if GetResourceState('oxmysql') == 'started' then
    testGetBusinessAccount()
    testSocietyFunctions()
else
    print("\n--- Skipping database tests (oxmysql not available) ---")
end

print("\n=== Test Complete ===")
print("The banking system now handles off-duty jobs correctly!")
print("When a job starts with 'off':")
print("  1. It will use the main job's bank account for all operations")
print("  2. It will NOT appear in the banking UI (filtered out)")
print("  3. No separate database entries are created for 'off' jobs")
